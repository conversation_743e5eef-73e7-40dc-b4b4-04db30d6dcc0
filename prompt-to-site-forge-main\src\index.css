
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=JetBrains+Mono:wght@100..800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    
    --primary: 252 87% 64%;
    --primary-foreground: 0 0% 100%;
    
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --radius: 0.5rem;
    
    --sidebar-background: 240 10% 3.9%;
    --sidebar-foreground: 240 5% 84.9%;
    --sidebar-primary: 252 87% 64%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 5% 84.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 240 4.9% 83.9%;

    --editor-background: 225 6% 13%;
    --editor-foreground: 0 0% 98%;
  }

  .light {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    
    --primary: 252 87% 64%;
    --primary-foreground: 0 0% 100%;
    
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.9% 10%;
    --sidebar-primary: 252 87% 64%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 240 5.9% 90%;
    --sidebar-ring: 240 5.9% 10%;

    --editor-background: 0 0% 96%;
    --editor-foreground: 240 10% 3.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground;
  }
  
  /* Button pulsating effect */
  .button-pulse {
    position: relative;
    overflow: hidden;
  }
  
  .button-pulse:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    box-shadow: 0 0 0 0 rgba(147, 112, 219, 0.6);
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(147, 112, 219, 0.6);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(147, 112, 219, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(147, 112, 219, 0);
    }
  }
}

/* Monaco Editor overrides */
.monaco-editor .margin,
.monaco-editor-background,
.monaco-editor .inputarea.ime-input {
  background-color: transparent !important;
}

.editor-wrapper {
  @apply h-full w-full overflow-hidden rounded-md;
}

/* Utility classes */
.drag-handle {
  @apply absolute top-0 right-0 w-1 h-full cursor-col-resize group-hover:bg-primary/50 transition-colors;
}

.splitter-layout {
  @apply flex h-full w-full;
}

.splitter-layout .layout-pane {
  @apply overflow-hidden;
}

.splitter-layout .layout-splitter {
  @apply bg-border cursor-col-resize hover:bg-primary/50 transition-colors;
  width: 4px;
  z-index: 1;
}

.glass-card {
  @apply bg-background/30 backdrop-blur-md border border-white/10;
}

.card-hover {
  @apply transition-all hover:shadow-md hover:border-primary/30 hover:-translate-y-0.5;
}

/* Preview iframe styles for full-size display */
.preview-iframe {
  width: 100% !important;
  height: 100% !important;
  border: 0 !important;
  display: block !important;
  min-height: 100% !important;
  max-height: none !important;
  overflow: hidden;
}

.preview-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Ensure ResizablePanel takes full height */
[data-panel-group-direction="horizontal"] > [data-panel] {
  height: 100%;
}

/* Force full height for all preview containers */
[data-panel] > div {
  height: 100%;
}

/* Ensure iframe containers take full height */
.preview-iframe-container {
  height: 100% !important;
  min-height: 100% !important;
}

/* Fix for WebContainer preview specifically */
.webcontainer-preview {
  height: 100% !important;
  min-height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
}

.webcontainer-preview iframe {
  height: 100% !important;
  min-height: 100% !important;
  flex: 1 !important;
}

/* Ensure WebContainer preview takes full available space */
.webcontainer-preview > div:last-child {
  flex: 1 !important;
  height: 100% !important;
}

.webcontainer-preview .preview-iframe-container {
  height: 100% !important;
  min-height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Debug styles for height issues (can be removed in production) */
.debug-height {
  outline: 2px solid red !important;
  outline-offset: -2px;
}

.debug-height::before {
  content: attr(data-debug);
  position: absolute;
  top: 0;
  left: 0;
  background: red;
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  z-index: 9999;
}
