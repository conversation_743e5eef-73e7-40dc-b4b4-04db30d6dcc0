
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { GeneratedFile } from '../types';
import { RefreshCw, Code, Maximize2, Minimize2, Smartphone, Tablet, Monitor, ExternalLink, Edit, Info, Container } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/card';
import { Toggle } from '@/components/ui/toggle';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import WebContainerPreview from './WebContainerPreview';

interface LivePreviewProps {
  files: GeneratedFile[];
  onRegenerateSection?: (sectionId: string, prompt: string) => Promise<void>;
  isProcessing?: boolean;
}

type ViewportSize = 'desktop' | 'tablet' | 'mobile';

const LivePreview: React.FC<LivePreviewProps> = ({
  files,
  onRegenerateSection,
  isProcessing = false
}) => {
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [cssContent, setCssContent] = useState<string>('');
  const [jsContent, setJsContent] = useState<string>('');
  const [isFullScreen, setIsFullScreen] = useState<boolean>(false);
  const [viewportSize, setViewportSize] = useState<ViewportSize>('desktop');
  const [sections, setSections] = useState<Array<{id: string, name: string}>>([]);
  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(null);
  const [regeneratePrompt, setRegeneratePrompt] = useState<string>('');
  const [isRegenerating, setIsRegenerating] = useState<boolean>(false);
  const [useWebContainer, setUseWebContainer] = useState<boolean>(true);
  const [webContainerError, setWebContainerError] = useState<string>('');
  const iframeRef = useRef<HTMLIFrameElement>(null);
  
  // Extract HTML, CSS, JS content from files
  useEffect(() => {
    console.log('LivePreview: Processing files:', files);
    
    if (files.length > 0) {
      let html = '';
      let css = '';
      let js = '';
      
      // Find HTML, CSS, and JS files
      const htmlFile = files.find(file => file.name.endsWith('.html'));
      const cssFile = files.find(file => file.name.endsWith('.css'));
      const jsFile = files.find(file => file.name.endsWith('.js'));
      
      if (htmlFile) {
        html = htmlFile.content || '';
        console.log('Found HTML content:', html.substring(0, 200) + '...');
      }
      if (cssFile) {
        css = cssFile.content || '';
        console.log('Found CSS content:', css.substring(0, 100) + '...');
      }
      if (jsFile) {
        js = jsFile.content || '';
        console.log('Found JS content:', js.substring(0, 100) + '...');
      }
      
      setHtmlContent(html);
      setCssContent(css);
      setJsContent(js);
      
      // Parse HTML to extract sections with data-section-id attributes
      if (html) {
        try {
          const parser = new DOMParser();
          const doc = parser.parseFromString(html, 'text/html');
          const sectionElements = doc.querySelectorAll('[data-section-id]');
          const extractedSections = Array.from(sectionElements).map(el => ({
            id: el.getAttribute('data-section-id') || '',
            name: el.getAttribute('data-section-name') || 'Unnamed Section'
          }));
          
          console.log('Extracted sections:', extractedSections);
          setSections(extractedSections);
        } catch (error) {
          console.error('Error parsing HTML for sections:', error);
        }
      }
    }
  }, [files]);

  // Update iframe content when files change
  useEffect(() => {
    if (htmlContent || cssContent || jsContent) {
      updateIframeContent();
    }
  }, [htmlContent, cssContent, jsContent]);

  const updateIframeContent = useCallback(() => {
    if (!iframeRef.current) return;
    
    console.log('Updating iframe with content');
    console.log('HTML length:', htmlContent.length);
    console.log('CSS length:', cssContent.length);
    console.log('JS length:', jsContent.length);
    
    const iframe = iframeRef.current;
    
    // Write content directly to iframe document
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (iframeDoc) {
      const combinedContent = createCombinedContent();
      console.log('Writing combined content to iframe:', combinedContent.substring(0, 500) + '...');
      
      iframeDoc.open();
      iframeDoc.write(combinedContent);
      iframeDoc.close();
    }
  }, [htmlContent, cssContent, jsContent]);
  
  const refreshPreview = useCallback(() => {
    updateIframeContent();
  }, [updateIframeContent]);
  
  const toggleFullScreen = useCallback(() => {
    setIsFullScreen(prev => !prev);
  }, []);

  const openInNewTab = useCallback(() => {
    const combinedContent = createCombinedContent();
    const blob = new Blob([combinedContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    
    const newWindow = window.open(url, '_blank');
    if (newWindow) {
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 1000);
    }
  }, [htmlContent, cssContent, jsContent]);

  const handleRegenerateSection = async () => {
    if (!selectedSectionId || !regeneratePrompt || !onRegenerateSection) {
      console.log('Missing requirements for regeneration:', { selectedSectionId, regeneratePrompt, onRegenerateSection });
      return;
    }
    
    setIsRegenerating(true);
    try {
      console.log('Regenerating section:', selectedSectionId, 'with prompt:', regeneratePrompt);
      await onRegenerateSection(selectedSectionId, regeneratePrompt);
      setRegeneratePrompt('');
      setSelectedSectionId(null);
      refreshPreview();
    } catch (error) {
      console.error('Failed to regenerate section:', error);
    } finally {
      setIsRegenerating(false);
    }
  };

  const createCombinedContent = () => {
    console.log('Creating combined content with:', { 
      htmlLength: htmlContent.length, 
      cssLength: cssContent.length, 
      jsLength: jsContent.length 
    });

    if (!htmlContent && !cssContent && !jsContent) {
      console.log('No content available, returning default template');
      return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Website</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            text-align: center; 
            margin: 0;
            background: #f5f5f5;
        }
        h1 { color: #666; }
    </style>
</head>
<body>
    <h1>No content generated yet</h1>
    <p>Generate a website using the form and your preview will appear here.</p>
</body>
</html>`;
    }
    
    // Check if HTML is a complete document
    const isCompleteHtml = htmlContent.includes('<!DOCTYPE') || 
                          htmlContent.includes('<html') || 
                          htmlContent.includes('<head>') || 
                          htmlContent.includes('<body>');
    
    if (isCompleteHtml) {
      console.log('Processing complete HTML document');
      let content = htmlContent;
      
      // Inject CSS if we have it and it's not already included
      if (cssContent) {
        console.log('Injecting CSS into complete HTML');
        const cssBlock = `<style>\n${cssContent}\n</style>`;
        
        if (content.includes('</head>')) {
          content = content.replace('</head>', `    ${cssBlock}\n</head>`);
        } else if (content.includes('<head>')) {
          content = content.replace('<head>', `<head>\n    ${cssBlock}`);
        } else {
          // Add head section if missing
          content = content.replace('<html>', `<html>\n<head>\n    ${cssBlock}\n</head>`);
        }
      }
      
      // Inject JS if we have it
      if (jsContent) {
        console.log('Injecting JS into complete HTML');
        const jsBlock = `<script>\n${jsContent}\n</script>`;
        
        if (content.includes('</body>')) {
          content = content.replace('</body>', `    ${jsBlock}\n</body>`);
        } else {
          content += `\n${jsBlock}`;
        }
      }
      
      console.log('Final complete HTML content length:', content.length);
      return content;
    }
    
    // Create complete HTML from fragments
    console.log('Creating complete HTML from fragments');
    const combinedContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Website</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif; 
            line-height: 1.6;
            color: #333;
        }
        
        /* Generated CSS */
        ${cssContent || '/* No custom CSS */'}
    </style>
</head>
<body>
    ${htmlContent || '<h1>No HTML content</h1>'}
    
    <script>
        // Generated JavaScript
        ${jsContent || 'console.log("Website loaded successfully");'}
    </script>
</body>
</html>`;

    console.log('Final fragment-based content length:', combinedContent.length);
    return combinedContent;
  };

  // Determine the appropriate width based on the selected viewport size
  const getPreviewWidth = () => {
    switch(viewportSize) {
      case 'mobile':
        return 'w-[375px]';
      case 'tablet':
        return 'w-[768px]';
      case 'desktop':
      default:
        return 'w-full';
    }
  };

  // Use WebContainer if enabled and files are available, but not if there's an error
  if (useWebContainer && files.length > 0 && !webContainerError) {
    return (
      <WebContainerPreview
        files={files}
        onRegenerateSection={onRegenerateSection}
        isProcessing={isProcessing}
        onError={(error) => {
          console.log('WebContainer error, falling back to iframe:', error);
          setWebContainerError(error);
          setUseWebContainer(false);
        }}
      />
    );
  }

  if (!htmlContent && !cssContent && !jsContent) {
    return (
      <Card className="flex flex-col h-full items-center justify-center bg-card/50 p-8 text-center">
        <div className="max-w-sm animate-fade-in">
          <Code className="h-16 w-16 text-muted-foreground mb-4 mx-auto opacity-70" />
          <p className="text-lg font-medium text-muted-foreground mb-2">No preview available</p>
          <p className="text-sm text-muted-foreground/80">
            Generate a website using the form above and your preview will appear here.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <div className={cn(
        "flex flex-col h-full border-l border-border/30 overflow-hidden transition-all duration-300 relative", 
        isFullScreen ? "fixed inset-0 z-50 bg-background" : ""
      )}>
        <div className="bg-card/60 backdrop-blur-sm p-3 border-b border-border/30 flex items-center justify-between sticky top-0 z-10">
          <div className="flex items-center">
            <Code className="h-4 w-4 mr-2 text-green-500" />
            <span className="text-sm font-medium text-foreground/90">
              Live Preview
            </span>
            <Badge variant="secondary" className="ml-2 text-xs">
              Iframe
            </Badge>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-2">
                  <Info className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <div className="space-y-2">
                  <p className="font-medium">Preview Mode: Iframe Preview</p>
                  <p className="text-xs">Using iframe-based preview for maximum compatibility</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </div>
          <div className="flex items-center gap-2">
            {/* Preview Mode Toggle */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setUseWebContainer(!useWebContainer);
                    if (webContainerError) {
                      setWebContainerError(''); // Clear error when manually switching
                    }
                  }}
                  className="h-8 px-2 border-border/40 mr-2"
                  title={webContainerError ? "WebContainer failed, using iframe" : "Switch to WebContainer preview"}
                >
                  <Container className="h-4 w-4 mr-1" />
                  <span className="text-xs">
                    {webContainerError ? 'Try WebContainer' : 'WebContainer'}
                  </span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <div className="max-w-xs">
                  {webContainerError ? (
                    <div>
                      <p className="font-medium text-red-400">WebContainer Error</p>
                      <p className="text-xs mt-1">{webContainerError}</p>
                      <p className="text-xs mt-1">Click to retry WebContainer mode</p>
                    </div>
                  ) : (
                    <p>Switch to WebContainer preview for better performance</p>
                  )}
                </div>
              </TooltipContent>
            </Tooltip>

            {/* Responsive View Toggle */}
            <div className="flex items-center mr-2 p-1 bg-background/50 rounded-md border border-border/40">
              <Toggle 
                pressed={viewportSize === 'desktop'} 
                onPressedChange={() => setViewportSize('desktop')}
                size="sm" 
                className="h-8 w-8 p-0 data-[state=on]:bg-primary/20"
                title="Desktop view"
              >
                <Monitor className="h-4 w-4" />
                <span className="sr-only">Desktop</span>
              </Toggle>
              <Toggle 
                pressed={viewportSize === 'tablet'} 
                onPressedChange={() => setViewportSize('tablet')}
                size="sm" 
                className="h-8 w-8 p-0 data-[state=on]:bg-primary/20"
                title="Tablet view"
              >
                <Tablet className="h-4 w-4" />
                <span className="sr-only">Tablet</span>
              </Toggle>
              <Toggle 
                pressed={viewportSize === 'mobile'} 
                onPressedChange={() => setViewportSize('mobile')}
                size="sm" 
                className="h-8 w-8 p-0 data-[state=on]:bg-primary/20"
                title="Mobile view"
              >
                <Smartphone className="h-4 w-4" />
                <span className="sr-only">Mobile</span>
              </Toggle>
            </div>
            
            {/* Section Regeneration */}
            {sections.length > 0 && onRegenerateSection && (
              <Popover>
                <PopoverTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    title="Edit section" 
                    className="h-8 border-border/40 mr-1"
                    disabled={isProcessing}
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    <span className="text-xs">Edit Section</span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-4" align="end">
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium">Regenerate Section</h4>
                    <div className="space-y-2">
                      <label htmlFor="section-select" className="text-xs text-muted-foreground">
                        Select section
                      </label>
                      <select 
                        id="section-select"
                        className="w-full rounded-md border border-border bg-background px-3 py-2 text-sm"
                        value={selectedSectionId || ''}
                        onChange={(e) => setSelectedSectionId(e.target.value)}
                      >
                        <option value="">Choose a section</option>
                        {sections.map(section => (
                          <option key={section.id} value={section.id}>{section.name}</option>
                        ))}
                      </select>
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="regenerate-prompt" className="text-xs text-muted-foreground">
                        New description
                      </label>
                      <textarea
                        id="regenerate-prompt"
                        placeholder="Describe how you want this section to look..."
                        className="w-full rounded-md border border-border bg-background px-3 py-2 text-sm min-h-[80px] resize-none"
                        value={regeneratePrompt}
                        onChange={(e) => setRegeneratePrompt(e.target.value)}
                      />
                    </div>
                    <Button 
                      onClick={handleRegenerateSection} 
                      disabled={!selectedSectionId || !regeneratePrompt || isRegenerating || isProcessing}
                      className="w-full"
                      size="sm"
                    >
                      {isRegenerating ? 'Regenerating...' : 'Regenerate Section'}
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            )}
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={refreshPreview} 
              title="Refresh preview" 
              className="h-8 w-8 p-0 border-border/40"
            >
              <RefreshCw className="h-4 w-4" />
              <span className="sr-only">Refresh</span>
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={toggleFullScreen} 
              title={isFullScreen ? "Exit fullscreen" : "Fullscreen preview"} 
              className="h-8 w-8 p-0 border-border/40"
            >
              {isFullScreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              <span className="sr-only">Fullscreen</span>
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={openInNewTab} 
              title="Open in new tab" 
              className="h-8 w-8 p-0 border-border/40"
            >
              <ExternalLink className="h-4 w-4" />
              <span className="sr-only">Open in new tab</span>
            </Button>
          </div>
        </div>
        
        <div className="flex-1 w-full h-full overflow-hidden">
          <div className={cn(
            "w-full h-full bg-white dark:bg-slate-900 flex justify-center",
            isFullScreen ? "h-[calc(100vh-60px)]" : "h-full"
          )}>
            <div className={cn(
              "transition-all duration-300 h-full preview-iframe-container",
              getPreviewWidth(),
              viewportSize !== 'desktop' && "border-x border-border/20 shadow-sm"
            )}>
              <iframe
                ref={iframeRef}
                title="Website Preview"
                className="preview-iframe"
                sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
              />
            </div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default LivePreview;
