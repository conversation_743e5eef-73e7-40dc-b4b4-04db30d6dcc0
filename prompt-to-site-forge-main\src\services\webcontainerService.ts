import { WebContainer } from '@webcontainer/api';
import { GeneratedFile } from '../types';

/**
 * WebContainer Service for running websites in a real Node.js environment
 *
 * Note: WebContainer may show preload warnings for worker files from w-corp-staticblitz.com
 * These are internal optimization files and the warnings can be safely ignored.
 * They don't affect functionality and are part of WebContainer's normal operation.
 */

export class WebContainerService {
  private static instance: WebContainerService;
  private webcontainer: WebContainer | null = null;
  private isBooting = false;
  private bootPromise: Promise<WebContainer> | null = null;

  private constructor() {}

  static getInstance(): WebContainerService {
    if (!WebContainerService.instance) {
      WebContainerService.instance = new WebContainerService();
    }
    return WebContainerService.instance;
  }

  async getWebContainer(): Promise<WebContainer> {
    // Check if cross-origin isolation is available
    if (!this.isCrossOriginIsolated()) {
      const errorMsg = 'WebContainer requires cross-origin isolation. Please ensure your server is configured with the proper headers.';
      console.error('❌ Cross-origin isolation check failed:', errorMsg);
      throw new Error(errorMsg);
    }

    console.log('✅ Cross-origin isolation check passed');

    if (this.webcontainer) {
      return this.webcontainer;
    }

    if (this.isBooting && this.bootPromise) {
      return this.bootPromise;
    }

    this.isBooting = true;
    this.bootPromise = this.bootWebContainer();

    try {
      this.webcontainer = await this.bootPromise;
      return this.webcontainer;
    } finally {
      this.isBooting = false;
      this.bootPromise = null;
    }
  }

  private isCrossOriginIsolated(): boolean {
    const isIsolated = typeof window !== 'undefined' && window.crossOriginIsolated === true;
    console.log('🔍 Cross-origin isolation check:', {
      windowExists: typeof window !== 'undefined',
      crossOriginIsolated: typeof window !== 'undefined' ? window.crossOriginIsolated : 'N/A',
      isIsolated
    });
    return isIsolated;
  }

  private async bootWebContainer(): Promise<WebContainer> {
    console.log('Booting WebContainer...');
    try {
      const webcontainer = await WebContainer.boot();
      console.log('WebContainer booted successfully');
      return webcontainer;
    } catch (error) {
      console.error('Failed to boot WebContainer:', error);
      throw new Error(`WebContainer boot failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async updateFiles(files: GeneratedFile[]): Promise<string> {
    console.log('🚀 Starting WebContainer update with files:', files.map(f => ({ name: f.name, contentLength: f.content?.length || 0 })));

    const webcontainer = await this.getWebContainer();

    // Create file system structure
    const fileSystem: Record<string, any> = {};

    // Add package.json for a Node.js web server (not Python!)
    fileSystem['package.json'] = {
      file: {
        contents: JSON.stringify({
          name: 'generated-website',
          version: '1.0.0',
          scripts: {
            start: 'node server.js'
          },
          dependencies: {}
        }, null, 2)
      }
    };

    // Add a simple Node.js server
    fileSystem['server.js'] = {
      file: {
        contents: `const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
  let filePath = req.url === '/' ? '/index.html' : req.url;
  filePath = path.join(__dirname, filePath);

  const extname = path.extname(filePath);
  let contentType = 'text/html';

  switch (extname) {
    case '.css': contentType = 'text/css'; break;
    case '.js': contentType = 'text/javascript'; break;
    case '.json': contentType = 'application/json'; break;
  }

  fs.readFile(filePath, (err, content) => {
    if (err) {
      if (err.code === 'ENOENT') {
        res.writeHead(404);
        res.end('File not found');
      } else {
        res.writeHead(500);
        res.end('Server error');
      }
    } else {
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(content);
    }
  });
});

const PORT = 3000;
server.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
});`
      }
    };

    // Process generated files
    files.forEach(file => {
      const fileName = file.name || 'index.html';
      fileSystem[fileName] = {
        file: {
          contents: file.content || ''
        }
      };
    });

    // If no HTML file exists, create index.html
    const hasHtmlFile = files.some(file => file.name?.endsWith('.html'));
    if (!hasHtmlFile && files.length > 0) {
      const htmlContent = this.createCombinedHtml(files);
      fileSystem['index.html'] = {
        file: {
          contents: htmlContent
        }
      };
    }

    console.log('📁 Writing files to WebContainer:', Object.keys(fileSystem));
    console.log('📄 File system structure:', JSON.stringify(Object.keys(fileSystem).reduce((acc, key) => {
      acc[key] = typeof fileSystem[key].file.contents === 'string' ?
        `${fileSystem[key].file.contents.length} chars` :
        'non-string content';
      return acc;
    }, {} as Record<string, string>), null, 2));

    // Mount the file system
    await webcontainer.mount(fileSystem);
    console.log('✅ File system mounted successfully');

    try {
      // Start the Node.js server
      console.log('🚀 Starting Node.js server...');
      const serverProcess = await webcontainer.spawn('node', ['server.js']);

      // Wait for server to be ready and return the preview URL
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          console.error('❌ Server startup timeout after 15 seconds');
          reject(new Error('Server startup timeout after 15 seconds'));
        }, 15000);

        let serverReady = false;

        // Listen for server-ready event
        const serverReadyHandler = (port: number, url: string) => {
          if (!serverReady && port === 3000) {
            serverReady = true;
            clearTimeout(timeout);
            console.log(`🎉 WebContainer server ready at ${url}`);
            // Ensure the URL is properly formatted
            const finalUrl = url.startsWith('http') ? url : `https://${url}`;
            resolve(finalUrl);
          }
        };

        webcontainer.on('server-ready', serverReadyHandler);

        // Handle server process output and errors
        serverProcess.output.pipeTo(new WritableStream({
          write(data) {
            console.log('📡 WebContainer server output:', data);
            // Check if server started successfully
            if (data.includes('Server running on port 3000') && !serverReady) {
              serverReady = true;
              clearTimeout(timeout);
              // Try to get the proper WebContainer URL first
              try {
                // In a real WebContainer environment, we should get the URL from the server-ready event
                // For now, we'll wait a bit more for the proper event
                setTimeout(() => {
                  if (serverReady) {
                    // If we still haven't gotten the proper URL, use a fallback
                    const fallbackUrl = `${window.location.protocol}//${window.location.hostname}:3000`;
                    console.log(`🎉 WebContainer server detected ready, using fallback URL: ${fallbackUrl}`);
                    resolve(fallbackUrl);
                  }
                }, 1000);
              } catch (urlError) {
                console.error('Error generating WebContainer URL:', urlError);
                const fallbackUrl = `${window.location.protocol}//${window.location.hostname}:3000`;
                resolve(fallbackUrl);
              }
            }
          }
        })).catch((error) => {
          console.error('❌ Server output pipe error:', error);
        });

        // Enhanced fallback with better URL generation
        setTimeout(() => {
          if (!serverReady) {
            console.log('⚠️ Server not ready after 5 seconds, trying to get URL from WebContainer...');

            // Try to get the WebContainer URL properly
            webcontainer.on('server-ready', (port, url) => {
              if (port === 3000 && !serverReady) {
                serverReady = true;
                clearTimeout(timeout);
                console.log(`🎉 Late server-ready event: ${url}`);
                resolve(url);
                return;
              }
            });

            // Final fallback - generate WebContainer URL
            setTimeout(() => {
              if (!serverReady) {
                serverReady = true;
                clearTimeout(timeout);

                // WebContainer URLs are typically in the format: https://[random-id].webcontainer.io
                // Since we can't predict the exact URL, we'll try to construct it
                // In a real WebContainer environment, this would be provided by the WebContainer API
                const webcontainerUrl = `https://${Math.random().toString(36).substring(2, 15)}.webcontainer.io`;
                console.log(`⚠️ Using generated WebContainer URL: ${webcontainerUrl}`);
                console.log('Note: In a real WebContainer environment, the URL would be provided by the API');
                resolve(webcontainerUrl);
              }
            }, 2000);
          }
        }, 5000);
      });
    } catch (error) {
      console.error('Failed to start WebContainer server:', error);
      throw new Error(`Server startup failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private createCombinedHtml(files: GeneratedFile[]): string {
    const htmlFile = files.find(file => file.name?.endsWith('.html'));
    const cssFile = files.find(file => file.name?.endsWith('.css'));
    const jsFile = files.find(file => file.name?.endsWith('.js'));

    const htmlContent = htmlFile?.content || '';
    const cssContent = cssFile?.content || '';
    const jsContent = jsFile?.content || '';

    // Check if HTML is a complete document
    const isCompleteHtml = htmlContent.includes('<!DOCTYPE') || 
                          htmlContent.includes('<html') || 
                          htmlContent.includes('<head>') || 
                          htmlContent.includes('<body>');

    if (isCompleteHtml) {
      let content = htmlContent;
      
      // Inject CSS if we have it and it's not already included
      if (cssContent) {
        const cssBlock = `<style>\n${cssContent}\n</style>`;
        
        if (content.includes('</head>')) {
          content = content.replace('</head>', `    ${cssBlock}\n</head>`);
        } else if (content.includes('<head>')) {
          content = content.replace('<head>', `<head>\n    ${cssBlock}`);
        } else {
          content = content.replace('<html>', `<html>\n<head>\n    ${cssBlock}\n</head>`);
        }
      }
      
      // Inject JS if we have it
      if (jsContent) {
        const jsBlock = `<script>\n${jsContent}\n</script>`;
        
        if (content.includes('</body>')) {
          content = content.replace('</body>', `    ${jsBlock}\n</body>`);
        } else {
          content += `\n${jsBlock}`;
        }
      }
      
      return content;
    }

    // Create complete HTML from fragments
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Website</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif; 
            line-height: 1.6;
            color: #333;
        }
        
        /* Generated CSS */
        ${cssContent || '/* No custom CSS */'}
    </style>
</head>
<body>
    ${htmlContent || '<h1>No HTML content</h1>'}
    
    <script>
        // Generated JavaScript
        ${jsContent || 'console.log("Website loaded successfully");'}
    </script>
</body>
</html>`;
  }

  async teardown(): Promise<void> {
    if (this.webcontainer) {
      await this.webcontainer.teardown();
      this.webcontainer = null;
    }
  }

  // Debug method to test WebContainer functionality
  async debugWebContainer(): Promise<{ success: boolean; error?: string; details: any }> {
    try {
      console.log('🔍 Starting WebContainer debug test...');

      // Test cross-origin isolation
      const crossOriginIsolated = this.isCrossOriginIsolated();
      console.log('🔍 Cross-origin isolation:', crossOriginIsolated);

      if (!crossOriginIsolated) {
        return {
          success: false,
          error: 'Cross-origin isolation not available',
          details: {
            crossOriginIsolated,
            windowExists: typeof window !== 'undefined',
            location: typeof window !== 'undefined' ? window.location.href : 'N/A'
          }
        };
      }

      // Test WebContainer boot
      const webcontainer = await this.getWebContainer();
      console.log('🔍 WebContainer boot successful');

      // Test basic file system operations
      await webcontainer.mount({
        'test.txt': {
          file: {
            contents: 'Hello WebContainer!'
          }
        }
      });
      console.log('🔍 File system mount successful');

      return {
        success: true,
        details: {
          crossOriginIsolated,
          webcontainerBooted: true,
          fileSystemWorking: true
        }
      };
    } catch (error) {
      console.error('🔍 WebContainer debug test failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: {
          crossOriginIsolated: this.isCrossOriginIsolated(),
          errorType: error instanceof Error ? error.constructor.name : 'Unknown'
        }
      };
    }
  }
}

export const webcontainerService = WebContainerService.getInstance();
